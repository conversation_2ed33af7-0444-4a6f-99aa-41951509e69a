"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Heart,
  Music,
  Baby,
  Coffee,
  BookOpen,
  Star
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function EventsPage() {
  const [selectedCategory, setSelectedCategory] = useState("all")

  const upcomingEvents = [
    {
      id: 1,
      title: "Easter Celebration Service",
      date: "2024-03-31",
      time: "9:00 AM & 11:00 AM",
      location: "Main Sanctuary",
      category: "worship",
      featured: true,
      description: "Join us for a special Easter celebration with music, testimonies, and the message of hope and resurrection.",
      image: "/images/events/easter-celebration-service.jpg",
      registrationRequired: false,
      capacity: null
    },
    {
      id: 2,
      title: "Community Food Drive",
      date: "2024-04-06",
      time: "10:00 AM - 2:00 PM",
      location: "Church Parking Lot",
      category: "outreach",
      featured: false,
      description: "Help us serve our community by donating non-perishable food items for local families in need.",
      image: "/images/events/community-food-drive.jpg",
      registrationRequired: false,
      capacity: null
    },
    {
      id: 3,
      title: "Youth Spring Retreat",
      date: "2024-04-13",
      time: "Friday 6:00 PM - Sunday 4:00 PM",
      location: "Camp Wilderness, IL",
      category: "youth",
      featured: true,
      description: "A weekend retreat for our youth group with activities, worship, fellowship, and spiritual growth.",
      image: "/images/events/youth-spring-retreat.jpg",
      registrationRequired: true,
      capacity: 50,
      registered: 32
    },
    {
      id: 4,
      title: "Women's Bible Study",
      date: "2024-04-15",
      time: "7:00 PM - 8:30 PM",
      location: "Fellowship Hall",
      category: "study",
      featured: false,
      description: "Weekly women's Bible study focusing on 'Proverbs 31: Living with Purpose and Grace'.",
      image: "/images/events/womens-bible-study.jpg",
      registrationRequired: true,
      capacity: 25,
      registered: 18
    },
    {
      id: 5,
      title: "Men's Breakfast & Fellowship",
      date: "2024-04-20",
      time: "8:00 AM - 10:00 AM",
      location: "Fellowship Hall",
      category: "fellowship",
      featured: false,
      description: "Monthly men's breakfast with great food, fellowship, and encouraging discussion.",
      image: "/images/events/mens-breakfast-fellowship.jpg",
      registrationRequired: true,
      capacity: 40,
      registered: 28
    },
    {
      id: 6,
      title: "Children's Easter Egg Hunt",
      date: "2024-04-22",
      time: "2:00 PM - 4:00 PM",
      location: "Church Grounds",
      category: "children",
      featured: false,
      description: "Fun-filled Easter egg hunt for children ages 0-12 with games, prizes, and refreshments.",
      image: "/images/events/childrens-easter-egg-hunt.jpg",
      registrationRequired: false,
      capacity: null
    },
    {
      id: 7,
      title: "Worship Night",
      date: "2024-04-28",
      time: "7:00 PM - 9:00 PM",
      location: "Main Sanctuary",
      category: "worship",
      featured: true,
      description: "An evening of worship, prayer, and spiritual renewal with our worship team and guest musicians.",
      image: "/images/events/worship-night-service.jpg",
      registrationRequired: false,
      capacity: null
    },
    {
      id: 8,
      title: "Vacation Bible School",
      date: "2024-06-10",
      time: "9:00 AM - 12:00 PM (Mon-Fri)",
      location: "Entire Church Campus",
      category: "children",
      featured: true,
      description: "Week-long VBS program for children with Bible stories, crafts, games, and music.",
      image: "/images/events/vacation-bible-school.jpg",
      registrationRequired: true,
      capacity: 100,
      registered: 45
    }
  ]

  const categories = [
    { id: "all", name: "All Events", icon: Calendar },
    { id: "worship", name: "Worship", icon: Music },
    { id: "youth", name: "Youth", icon: Users },
    { id: "children", name: "Children", icon: Baby },
    { id: "study", name: "Bible Study", icon: BookOpen },
    { id: "fellowship", name: "Fellowship", icon: Coffee },
    { id: "outreach", name: "Outreach", icon: Heart }
  ]

  const filteredEvents = selectedCategory === "all" 
    ? upcomingEvents 
    : upcomingEvents.filter(event => event.category === selectedCategory)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Church Events</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Join us for worship, fellowship, and community events throughout the year
          </p>
        </div>
      </section>

      {/* Event Categories */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map((category) => {
              const IconComponent = category.icon
              return (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center gap-2"
                >
                  <IconComponent className="w-4 h-4" />
                  {category.name}
                </Button>
              )
            })}
          </div>
        </div>
      </section>

      {/* Featured Events */}
      {selectedCategory === "all" && (
        <section className="py-16 bg-blue-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center">Featured Events</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {upcomingEvents.filter(event => event.featured).map((event) => (
                <Card key={event.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative h-48">
                    <Image
                      src={event.image}
                      alt={event.title}
                      fill
                      className="object-cover"
                    />
                    <Badge className="absolute top-4 left-4 bg-yellow-500 text-yellow-900">
                      <Star className="w-3 h-3 mr-1" />
                      Featured
                    </Badge>
                  </div>
                  <CardHeader>
                    <CardTitle className="text-xl">{event.title}</CardTitle>
                    <CardDescription className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="w-4 h-4" />
                        {formatDate(event.date)}
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="w-4 h-4" />
                        {event.time}
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="w-4 h-4" />
                        {event.location}
                      </div>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">{event.description}</p>
                    {event.registrationRequired && (
                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>Registration</span>
                          <span>{event.registered}/{event.capacity}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(event.registered! / event.capacity!) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                    <Button className="w-full">
                      {event.registrationRequired ? "Register Now" : "Learn More"}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* All Events */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center">
            {selectedCategory === "all" ? "All Upcoming Events" : `${categories.find(c => c.id === selectedCategory)?.name} Events`}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredEvents.map((event) => (
              <Card key={event.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative h-48">
                  <Image
                    src={event.image}
                    alt={event.title}
                    fill
                    className="object-cover"
                  />
                  {event.featured && (
                    <Badge className="absolute top-4 left-4 bg-yellow-500 text-yellow-900">
                      <Star className="w-3 h-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                </div>
                <CardHeader>
                  <CardTitle className="text-xl">{event.title}</CardTitle>
                  <CardDescription className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="w-4 h-4" />
                      {formatDate(event.date)}
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="w-4 h-4" />
                      {event.time}
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="w-4 h-4" />
                      {event.location}
                    </div>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">{event.description}</p>
                  {event.registrationRequired && event.capacity && (
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Registration</span>
                        <span>{event.registered}/{event.capacity}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(event.registered! / event.capacity!) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                  <Button className="w-full">
                    {event.registrationRequired ? "Register Now" : "Learn More"}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Stay Connected</h2>
          <p className="text-xl mb-8 text-blue-100">
            Don't miss out on upcoming events and special announcements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
                Subscribe to Newsletter
              </Button>
            </Link>
            <Link href="/contact">
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
