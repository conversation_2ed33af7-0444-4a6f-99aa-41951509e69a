# Church Website Image Sources and Implementation Guide

## High-Quality Stock Photo Sources

### 1. **Unsplash** (https://unsplash.com)
- **Search Terms**: "church", "worship", "community", "prayer", "bible study", "children ministry"
- **License**: Free for commercial and personal use
- **Quality**: High-resolution, professional photography
- **Best For**: Hero images, worship scenes, community gatherings

### 2. **Pexels** (https://pexels.com)
- **Search Terms**: "church building", "congregation", "pastor", "fellowship"
- **License**: Free for commercial use
- **Quality**: High-resolution stock photos
- **Best For**: Church exteriors, group activities, ministry photos

### 3. **Pixabay** (https://pixabay.com)
- **Search Terms**: "church interior", "stained glass", "cross", "bible"
- **License**: Free for commercial use
- **Quality**: Good resolution, varied styles
- **Best For**: Religious symbols, church architecture, backgrounds

### 4. **StockVault** (https://stockvault.net)
- **Search Terms**: "religious", "christian", "worship service"
- **License**: Free with attribution
- **Quality**: Professional stock photos
- **Best For**: Worship services, religious imagery

## Recommended Images by Category

### Hero Section Images (1920x1080px)
1. **Church Exterior**: Modern church building with welcoming entrance
2. **Worship Service**: Congregation during worship with raised hands
3. **Community Gathering**: People fellowshipping and talking
4. **Pastor Preaching**: Pastor at pulpit with congregation
5. **Church Interior**: Beautiful sanctuary with natural lighting

### Ministry Photos (800x600px)
1. **Children's Ministry**: Kids in classroom setting with teacher
2. **Youth Group**: Teenagers in circle discussion or activity
3. **Worship Team**: Musicians and singers performing
4. **Small Group**: Adults in Bible study circle
5. **Community Service**: Volunteers serving food or helping others
6. **Fellowship**: People enjoying coffee and conversation

### Staff Photos (400x400px)
1. **Professional Headshots**: Clean background, business casual attire
2. **Consistent Lighting**: Same lighting setup for all staff
3. **Friendly Expression**: Warm, approachable smiles
4. **High Resolution**: Sharp, clear images

### Event Photos (600x400px)
1. **Easter Service**: Special worship service with decorations
2. **Community Events**: Outdoor gatherings, food drives
3. **Youth Activities**: Retreats, camps, group activities
4. **Bible Studies**: Small groups with Bibles and materials
5. **Fellowship Meals**: People sharing meals together
6. **Children's Events**: Kids activities, VBS, Easter egg hunts

### Sermon Series Artwork (600x400px)
1. **Hope Series**: Sunrise, light breaking through clouds
2. **Gospel of John**: Open Bible, cross, light imagery
3. **Psalms Series**: Nature scenes, peaceful landscapes
4. **Easter Series**: Empty tomb, resurrection imagery
5. **Christmas Series**: Nativity, star, warm lighting

## Image Optimization Guidelines

### File Formats
- **Photos**: Use `.jpg` for photographs (smaller file size)
- **Graphics**: Use `.png` for graphics with transparency
- **Icons**: Use `.svg` when possible for scalability

### Compression Settings
- **Quality**: 80-85% for web use (balance of quality and file size)
- **Progressive JPEG**: Enable for better loading experience
- **WebP Format**: Consider for modern browsers (Next.js supports automatic conversion)

### Responsive Sizes
- **Desktop**: Full resolution (1920px width max)
- **Tablet**: Medium resolution (1024px width)
- **Mobile**: Smaller resolution (640px width)
- **Thumbnails**: 300px width for cards and previews

## Implementation Steps

### Step 1: Download and Organize
```bash
public/images/
├── hero/
│   ├── church-exterior.jpg
│   ├── worship-service.jpg
│   └── community-gathering.jpg
├── about/
│   └── church-history.jpg
├── ministries/
│   ├── children-ministry.jpg
│   ├── youth-group.jpg
│   ├── worship-team.jpg
│   ├── small-group.jpg
│   ├── community-outreach.jpg
│   └── hospitality.jpg
├── staff/
│   ├── pastor-john-smith.jpg
│   ├── sarah-johnson.jpg
│   ├── mike-davis.jpg
│   └── lisa-brown.jpg
├── events/
│   ├── easter-service.jpg
│   ├── food-drive.jpg
│   ├── youth-retreat.jpg
│   └── bible-study.jpg
└── sermons/
    ├── hope-series.jpg
    ├── john-series.jpg
    └── psalms-series.jpg
```

### Step 2: Image Processing Tools
- **Online**: TinyPNG, Squoosh.app, Canva
- **Desktop**: Photoshop, GIMP, ImageOptim
- **Command Line**: ImageMagick, Sharp

### Step 3: Batch Processing Script
```bash
# Example ImageMagick commands for batch processing
mogrify -resize 1920x1080^ -gravity center -crop 1920x1080+0+0 *.jpg
mogrify -quality 85 *.jpg
```

## Next.js Image Optimization

### Using Next.js Image Component
```jsx
import Image from "next/image"

// Optimized image loading
<Image
  src="/images/hero/church-exterior.jpg"
  alt="Grace Community Church exterior"
  width={1920}
  height={1080}
  priority // For above-the-fold images
  className="object-cover"
/>
```

### Image Loading Strategies
- **Priority**: Use for hero images and above-the-fold content
- **Lazy Loading**: Default for images below the fold
- **Placeholder**: Use blur or solid color while loading
- **Responsive**: Automatically serves appropriate sizes

## Copyright and Attribution

### Free Stock Photos
- **Unsplash**: No attribution required but appreciated
- **Pexels**: No attribution required but appreciated
- **Pixabay**: No attribution required but appreciated

### Best Practices
- Keep records of image sources and licenses
- Provide attribution when required
- Avoid images with recognizable people without permission
- Ensure images align with church values and message

## Quality Checklist

### Before Upload
- [ ] Proper resolution for intended use
- [ ] Optimized file size (under 500KB for most images)
- [ ] Appropriate aspect ratio
- [ ] Good lighting and composition
- [ ] Relevant to content and church mission

### After Upload
- [ ] Images load quickly on all devices
- [ ] Responsive behavior works correctly
- [ ] Alt text is descriptive and meaningful
- [ ] Images enhance rather than distract from content
- [ ] Consistent style and quality across site

## Fallback Strategy

### If Specific Photos Aren't Available
1. **Generic Church Stock Photos**: Use high-quality generic church images
2. **Illustrated Graphics**: Create simple, clean graphics with church colors
3. **Solid Color Backgrounds**: Use church brand colors with text overlay
4. **Gradients**: Maintain existing gradient backgrounds until photos available

This systematic approach ensures professional, optimized images that enhance your church website's visual appeal and user experience.
