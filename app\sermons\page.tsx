"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Play, 
  Download, 
  Calendar, 
  Clock, 
  User, 
  Search,
  BookOpen,
  Volume2,
  Video,
  FileText,
  Star
} from "lucide-react"
import Image from "next/image"

export default function SermonsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSeries, setSelectedSeries] = useState("all")

  const sermonSeries = [
    {
      id: "all",
      name: "All Sermons",
      count: 45
    },
    {
      id: "hope-in-hard-times",
      name: "Hope in Hard Times",
      count: 8,
      description: "Finding God's presence and purpose in life's challenges",
      image: "/images/sermons/hope-in-hard-times-series.jpg",
      current: true
    },
    {
      id: "gospel-of-john",
      name: "Gospel of John",
      count: 12,
      description: "Exploring the life and teachings of Jesus through <PERSON>'s Gospel",
      image: "/images/sermons/gospel-of-john-series.jpg",
      current: false
    },
    {
      id: "psalms-for-life",
      name: "Psalms for Life",
      count: 10,
      description: "Finding comfort, wisdom, and worship in the Psalms",
      image: "/images/sermons/psalms-for-life-series.jpg",
      current: false
    },
    {
      id: "easter-2024",
      name: "Easter 2024",
      count: 4,
      description: "Celebrating the resurrection and new life in Christ",
      image: "/images/sermons/easter-2024-series.jpg",
      current: false
    },
    {
      id: "christmas-2023",
      name: "Christmas 2023",
      count: 4,
      description: "The wonder and joy of Christ's birth",
      image: "/images/sermons/christmas-2023-series.jpg",
      current: false
    }
  ]

  const sermons = [
    {
      id: 1,
      title: "When God Seems Silent",
      speaker: "Pastor John Smith",
      date: "2024-03-24",
      duration: "35:42",
      series: "hope-in-hard-times",
      seriesName: "Hope in Hard Times",
      description: "Learning to trust God's presence even when we can't hear His voice clearly.",
      scripture: "Psalm 13:1-6",
      image: "/images/sermons/when-god-seems-silent.jpg",
      audioUrl: "/audio/sermon-1.mp3",
      videoUrl: "/video/sermon-1.mp4",
      notesUrl: "/notes/sermon-1.pdf",
      featured: true
    },
    {
      id: 2,
      title: "The Light of the World",
      speaker: "Pastor John Smith",
      date: "2024-03-17",
      duration: "42:18",
      series: "gospel-of-john",
      seriesName: "Gospel of John",
      description: "Jesus declares Himself as the light that overcomes all darkness.",
      scripture: "John 8:12-20",
      image: "/images/sermons/light-of-the-world.jpg",
      audioUrl: "/audio/sermon-2.mp3",
      videoUrl: "/video/sermon-2.mp4",
      notesUrl: "/notes/sermon-2.pdf",
      featured: false
    },
    {
      id: 3,
      title: "Finding Peace in the Storm",
      speaker: "Pastor John Smith",
      date: "2024-03-10",
      duration: "38:25",
      series: "hope-in-hard-times",
      seriesName: "Hope in Hard Times",
      description: "How to maintain inner peace when life feels chaotic and uncertain.",
      scripture: "Mark 4:35-41",
      image: "/images/sermons/peace-in-the-storm.jpg",
      audioUrl: "/audio/sermon-3.mp3",
      videoUrl: "/video/sermon-3.mp4",
      notesUrl: "/notes/sermon-3.pdf",
      featured: true
    },
    {
      id: 4,
      title: "The Good Shepherd",
      speaker: "Pastor John Smith",
      date: "2024-03-03",
      duration: "40:12",
      series: "gospel-of-john",
      seriesName: "Gospel of John",
      description: "Understanding Jesus as our caring, protective, and guiding shepherd.",
      scripture: "John 10:1-18",
      image: "/images/sermons/the-good-shepherd.jpg",
      audioUrl: "/audio/sermon-4.mp3",
      videoUrl: "/video/sermon-4.mp4",
      notesUrl: "/notes/sermon-4.pdf",
      featured: false
    },
    {
      id: 5,
      title: "The Lord is My Shepherd",
      speaker: "Pastor John Smith",
      date: "2024-02-25",
      duration: "36:30",
      series: "psalms-for-life",
      seriesName: "Psalms for Life",
      description: "Finding comfort and guidance in the most beloved psalm.",
      scripture: "Psalm 23",
      image: "/images/sermons/lord-is-my-shepherd.jpg",
      audioUrl: "/audio/sermon-5.mp3",
      videoUrl: "/video/sermon-5.mp4",
      notesUrl: "/notes/sermon-5.pdf",
      featured: false
    },
    {
      id: 6,
      title: "He is Risen Indeed",
      speaker: "Pastor John Smith",
      date: "2024-03-31",
      duration: "45:15",
      series: "easter-2024",
      seriesName: "Easter 2024",
      description: "Celebrating the victory of Christ over death and what it means for us today.",
      scripture: "Luke 24:1-12",
      image: "/images/sermons/he-is-risen-indeed.jpg",
      audioUrl: "/audio/sermon-6.mp3",
      videoUrl: "/video/sermon-6.mp4",
      notesUrl: "/notes/sermon-6.pdf",
      featured: true
    }
  ]

  const filteredSermons = sermons.filter(sermon => {
    const matchesSearch = sermon.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sermon.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sermon.scripture.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesSeries = selectedSeries === "all" || sermon.series === selectedSeries
    return matchesSearch && matchesSeries
  })

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Sermons</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Listen to God's Word taught with passion and clarity
          </p>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search sermons by title, topic, or scripture..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 py-3"
              />
            </div>
            
            <div className="flex flex-wrap gap-2 justify-center">
              {sermonSeries.map((series) => (
                <Button
                  key={series.id}
                  variant={selectedSeries === series.id ? "default" : "outline"}
                  onClick={() => setSelectedSeries(series.id)}
                  className="flex items-center gap-2"
                >
                  {series.name}
                  <Badge variant="secondary" className="ml-1">
                    {series.count}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Current Series Highlight */}
      {selectedSeries === "all" && (
        <section className="py-16 bg-blue-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center">Current Series</h2>
            {sermonSeries.filter(series => series.current).map((series) => (
              <Card key={series.id} className="max-w-4xl mx-auto overflow-hidden">
                <div className="grid lg:grid-cols-2 gap-0">
                  <div className="relative h-64 lg:h-auto">
                    <Image
                      src={series.image!}
                      alt={series.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-8">
                    <Badge className="mb-4 bg-yellow-500 text-yellow-900">
                      <Star className="w-3 h-3 mr-1" />
                      Current Series
                    </Badge>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{series.name}</h3>
                    <p className="text-gray-700 mb-6">{series.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-6">
                      <div className="flex items-center gap-2">
                        <BookOpen className="w-4 h-4" />
                        {series.count} Messages
                      </div>
                    </div>
                    <Button onClick={() => setSelectedSeries(series.id)}>
                      View Series
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </section>
      )}

      {/* Sermons List */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center">
            {selectedSeries === "all" ? "Recent Sermons" : sermonSeries.find(s => s.id === selectedSeries)?.name}
          </h2>
          
          {filteredSermons.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600 text-lg">No sermons found matching your search criteria.</p>
            </div>
          ) : (
            <div className="grid gap-6">
              {filteredSermons.map((sermon) => (
                <Card key={sermon.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="grid lg:grid-cols-3 gap-0">
                    <div className="relative h-48 lg:h-auto">
                      <Image
                        src={sermon.image}
                        alt={sermon.title}
                        fill
                        className="object-cover"
                      />
                      {sermon.featured && (
                        <Badge className="absolute top-4 left-4 bg-yellow-500 text-yellow-900">
                          <Star className="w-3 h-3 mr-1" />
                          Featured
                        </Badge>
                      )}
                    </div>
                    <div className="lg:col-span-2 p-6">
                      <div className="flex flex-wrap gap-2 mb-3">
                        <Badge variant="outline">{sermon.seriesName}</Badge>
                        <Badge variant="secondary">{sermon.scripture}</Badge>
                      </div>
                      
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{sermon.title}</h3>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4" />
                          {sermon.speaker}
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          {formatDate(sermon.date)}
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          {sermon.duration}
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-4">{sermon.description}</p>
                      
                      <div className="flex flex-wrap gap-2">
                        <Button size="sm" className="flex items-center gap-2">
                          <Play className="w-4 h-4" />
                          Play Audio
                        </Button>
                        <Button size="sm" variant="outline" className="flex items-center gap-2">
                          <Video className="w-4 h-4" />
                          Watch Video
                        </Button>
                        <Button size="sm" variant="outline" className="flex items-center gap-2">
                          <FileText className="w-4 h-4" />
                          Notes
                        </Button>
                        <Button size="sm" variant="outline" className="flex items-center gap-2">
                          <Download className="w-4 h-4" />
                          Download
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Never Miss a Message</h2>
          <p className="text-xl mb-8 text-blue-100">
            Subscribe to our podcast or follow us on social media to stay updated.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
              Subscribe to Podcast
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-blue-600"
            >
              Follow on YouTube
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
