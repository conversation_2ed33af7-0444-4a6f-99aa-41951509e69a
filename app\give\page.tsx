"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Heart, CreditCard, DollarSign, Users, Home, Globe } from "lucide-react"

export default function GivePage() {
  const [amount, setAmount] = useState("")
  const [frequency, setFrequency] = useState("one-time")
  const [fund, setFund] = useState("general")

  const quickAmounts = [25, 50, 100, 250, 500]

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Give Online</h1>
          <p className="text-xl text-green-100 max-w-3xl mx-auto">
            Your generosity helps us share God's love and serve our community. Thank you for partnering with us in
            ministry.
          </p>
        </div>
      </section>

      {/* Giving Form */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Giving Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="w-6 h-6 text-red-500" />
                    Make a Donation
                  </CardTitle>
                  <CardDescription>Choose your donation amount and frequency below</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Amount Selection */}
                  <div>
                    <Label className="text-base font-semibold mb-3 block">Donation Amount</Label>
                    <div className="grid grid-cols-3 md:grid-cols-5 gap-2 mb-4">
                      {quickAmounts.map((quickAmount) => (
                        <Button
                          key={quickAmount}
                          variant={amount === quickAmount.toString() ? "default" : "outline"}
                          onClick={() => setAmount(quickAmount.toString())}
                          className="h-12"
                        >
                          ${quickAmount}
                        </Button>
                      ))}
                    </div>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="Enter custom amount"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  {/* Frequency Selection */}
                  <div>
                    <Label className="text-base font-semibold mb-3 block">Frequency</Label>
                    <RadioGroup value={frequency} onValueChange={setFrequency}>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="one-time" id="one-time" />
                        <Label htmlFor="one-time">One-time donation</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="weekly" id="weekly" />
                        <Label htmlFor="weekly">Weekly</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="monthly" id="monthly" />
                        <Label htmlFor="monthly">Monthly</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Fund Selection */}
                  <div>
                    <Label className="text-base font-semibold mb-3 block">Designation</Label>
                    <Select value={fund} onValueChange={setFund}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">General Fund</SelectItem>
                        <SelectItem value="missions">Missions</SelectItem>
                        <SelectItem value="building">Building Fund</SelectItem>
                        <SelectItem value="youth">Youth Ministry</SelectItem>
                        <SelectItem value="children">Children's Ministry</SelectItem>
                        <SelectItem value="benevolence">Benevolence Fund</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Payment Method */}
                  <div>
                    <Label className="text-base font-semibold mb-3 block">Payment Method</Label>
                    <div className="grid md:grid-cols-2 gap-4">
                      <Button variant="outline" className="h-16 flex flex-col items-center gap-2">
                        <CreditCard className="w-6 h-6" />
                        Credit/Debit Card
                      </Button>
                      <Button variant="outline" className="h-16 flex flex-col items-center gap-2">
                        <DollarSign className="w-6 h-6" />
                        Bank Transfer
                      </Button>
                    </div>
                  </div>

                  <Button size="lg" className="w-full">
                    Continue to Payment
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Giving Information */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Why We Give</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">
                    Giving is an act of worship and a way to participate in God's work. Your donations help us:
                  </p>
                  <ul className="mt-4 space-y-2 text-sm text-gray-600">
                    <li>• Support our weekly ministries</li>
                    <li>• Maintain our church facilities</li>
                    <li>• Fund mission trips and outreach</li>
                    <li>• Provide resources for families</li>
                    <li>• Help those in need in our community</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Secure Giving</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">
                    Your donation is secure and encrypted. We use industry-standard security measures to protect your
                    information.
                  </p>
                  <div className="mt-4 flex items-center gap-2 text-sm text-gray-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    SSL Encrypted
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Tax Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">
                    Grace Community Church is a 501(c)(3) organization. Your donation is tax-deductible to the extent
                    allowed by law.
                  </p>
                  <p className="text-gray-600 text-sm mt-2">Tax ID: 12-3456789</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Your Impact</h2>
            <p className="text-lg text-gray-600">See how your generosity is making a difference</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardHeader>
                <Users className="w-12 h-12 mx-auto text-blue-500 mb-2" />
                <CardTitle>850+</CardTitle>
                <CardDescription>People served monthly</CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Home className="w-12 h-12 mx-auto text-green-500 mb-2" />
                <CardTitle>25</CardTitle>
                <CardDescription>Families supported</CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Globe className="w-12 h-12 mx-auto text-purple-500 mb-2" />
                <CardTitle>5</CardTitle>
                <CardDescription>Mission trips funded</CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Heart className="w-12 h-12 mx-auto text-red-500 mb-2" />
                <CardTitle>$50K</CardTitle>
                <CardDescription>Community aid provided</CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Other Ways to Give */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Other Ways to Give</h2>
            <p className="text-lg text-gray-600">Additional options for your generosity</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Mail a Check</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4">Send your donation by mail to:</p>
                <div className="text-sm text-gray-700">
                  <p>Grace Community Church</p>
                  <p>123 Faith Street</p>
                  <p>Springfield, IL 62701</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Give in Person</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm">
                  Drop your donation in the offering box during any service or visit our church office during business
                  hours.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Stock Donations</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm">
                  For stock or other asset donations, please contact our finance <NAME_EMAIL>
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
