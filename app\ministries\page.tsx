import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Baby, 
  Users, 
  Music, 
  BookOpen, 
  Heart, 
  Globe, 
  Coffee, 
  Handshake,
  Calendar,
  Clock,
  Mail,
  Phone
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function MinistriesPage() {
  const ministries = [
    {
      id: "children",
      name: "Children's Ministry",
      icon: Baby,
      color: "text-pink-500",
      bgColor: "bg-pink-50",
      ageRange: "Ages 0-12",
      description: "Creating a fun, safe, and engaging environment where children can learn about God's love through age-appropriate activities, games, and Bible stories.",
      programs: [
        "Sunday School (Ages 3-12)",
        "Nursery Care (Ages 0-2)",
        "Vacation Bible School",
        "Children's Choir",
        "Family Fun Nights"
      ],
      meetingTime: "Sundays 9:00 AM & 11:00 AM",
      contact: "<PERSON> - l<PERSON>@gracecommunity.org",
      image: "/images/ministries/children-ministry-classroom.jpg"
    },
    {
      id: "youth",
      name: "Youth Ministry",
      icon: Users,
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      ageRange: "Ages 13-18",
      description: "Empowering teenagers to grow in their faith, build lasting friendships, and discover their purpose in God's plan through dynamic teaching and exciting activities.",
      programs: [
        "Wednesday Night Youth Group",
        "Youth Sunday School",
        "Summer Mission Trips",
        "Youth Retreats",
        "Confirmation Classes"
      ],
      meetingTime: "Wednesdays 7:00 PM",
      contact: "Mike Davis - <EMAIL>",
      image: "/images/ministries/youth-group-activity.jpg"
    },
    {
      id: "worship",
      name: "Worship Ministry",
      icon: Music,
      color: "text-purple-500",
      bgColor: "bg-purple-50",
      ageRange: "All Ages",
      description: "Leading our congregation in heartfelt worship through music, creating an atmosphere where people can encounter God and express their love for Him.",
      programs: [
        "Sunday Worship Team",
        "Choir",
        "Instrumental Ensemble",
        "Sound & Media Team",
        "Worship Leader Training"
      ],
      meetingTime: "Thursdays 7:00 PM (Practice)",
      contact: "Sarah Johnson - <EMAIL>",
      image: "/images/ministries/worship-team-performing.jpg"
    },
    {
      id: "small-groups",
      name: "Small Groups",
      icon: BookOpen,
      color: "text-green-500",
      bgColor: "bg-green-50",
      ageRange: "Adults",
      description: "Building deeper relationships and growing in faith through Bible study, prayer, and fellowship in intimate group settings throughout the week.",
      programs: [
        "Adult Bible Study Groups",
        "Men's Ministry",
        "Women's Ministry",
        "Couples Groups",
        "Senior Adult Ministry"
      ],
      meetingTime: "Various times throughout the week",
      contact: "Pastor John Smith - <EMAIL>",
      image: "/images/ministries/small-group-bible-study.jpg"
    },
    {
      id: "outreach",
      name: "Community Outreach",
      icon: Heart,
      color: "text-red-500",
      bgColor: "bg-red-50",
      ageRange: "All Ages",
      description: "Serving our local community and beyond through acts of love, compassion, and practical assistance to those in need.",
      programs: [
        "Food Pantry",
        "Community Garden",
        "Homeless Ministry",
        "Prison Ministry",
        "International Missions"
      ],
      meetingTime: "Saturdays 9:00 AM",
      contact: "<EMAIL>",
      image: "/images/ministries/community-outreach-service.jpg"
    },
    {
      id: "hospitality",
      name: "Hospitality Ministry",
      icon: Coffee,
      color: "text-orange-500",
      bgColor: "bg-orange-50",
      ageRange: "All Ages",
      description: "Creating a welcoming environment for all who enter our doors, ensuring everyone feels valued and cared for from the moment they arrive.",
      programs: [
        "Greeting Team",
        "Coffee & Fellowship",
        "New Member Integration",
        "Special Events Catering",
        "Visitor Follow-up"
      ],
      meetingTime: "Sundays (All Services)",
      contact: "<EMAIL>",
      image: "/images/ministries/hospitality-coffee-fellowship.jpg"
    }
  ]

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Ministries</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Discover your place in our church family through meaningful ministry opportunities
          </p>
        </div>
      </section>

      {/* Ministry Overview */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Find Your Ministry</h2>
            <p className="text-lg text-gray-700">
              At Grace Community Church, we believe everyone has been gifted by God to serve others. 
              Whether you're looking to grow in your faith, serve your community, or use your talents 
              for God's glory, there's a place for you in our ministry family.
            </p>
          </div>
        </div>
      </section>

      {/* Ministries Grid */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid gap-8">
            {ministries.map((ministry, index) => {
              const IconComponent = ministry.icon
              return (
                <Card key={ministry.id} className="overflow-hidden">
                  <div className="grid lg:grid-cols-2 gap-0">
                    <div className="relative h-64 lg:h-auto">
                      <Image
                        src={ministry.image}
                        alt={ministry.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-8">
                      <div className="flex items-center gap-3 mb-4">
                        <div className={`p-3 rounded-full ${ministry.bgColor}`}>
                          <IconComponent className={`w-8 h-8 ${ministry.color}`} />
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">{ministry.name}</h3>
                          <Badge variant="secondary">{ministry.ageRange}</Badge>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-6">{ministry.description}</p>
                      
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Programs & Activities:</h4>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {ministry.programs.map((program, idx) => (
                              <li key={idx} className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                                {program}
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4" />
                            {ministry.meetingTime}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Mail className="w-4 h-4" />
                          {ministry.contact}
                        </div>
                        
                        <Button className="mt-4">
                          Get Involved
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Get Involved CTA */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Get Involved?</h2>
          <p className="text-xl mb-8 text-blue-100">
            We'd love to help you find the perfect ministry opportunity to serve and grow.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
                Contact Us
              </Button>
            </Link>
            <Link href="/visit">
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                Plan Your Visit
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
