"use client"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

interface RotatingTextProps {
  texts: string[]
  interval?: number
  className?: string
  animationType?: "fade" | "slide" | "typewriter"
  prefix?: string
  suffix?: string
}

export function RotatingText({ 
  texts, 
  interval = 3000, 
  className = "",
  animationType = "fade",
  prefix = "",
  suffix = ""
}: RotatingTextProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (texts.length <= 1) return

    const timer = setInterval(() => {
      setIsAnimating(true)
      
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % texts.length)
        setIsAnimating(false)
      }, animationType === "fade" ? 300 : 500)
    }, interval)

    return () => clearInterval(timer)
  }, [texts.length, interval, animationType])

  if (texts.length === 0) return null

  const getAnimationClasses = () => {
    switch (animationType) {
      case "fade":
        return {
          container: "transition-all duration-500 ease-in-out",
          text: isAnimating
            ? "opacity-0 transform translate-y-2 scale-95"
            : "opacity-100 transform translate-y-0 scale-100"
        }
      case "slide":
        return {
          container: "transition-all duration-600 ease-in-out",
          text: isAnimating
            ? "transform translate-x-4 opacity-0 blur-sm"
            : "transform translate-x-0 opacity-100 blur-0"
        }
      case "typewriter":
        return {
          container: "transition-all duration-300",
          text: "animate-pulse"
        }
      default:
        return {
          container: "transition-all duration-400 ease-in-out",
          text: isAnimating ? "opacity-0" : "opacity-100"
        }
    }
  }

  const animationClasses = getAnimationClasses()

  return (
    <span className={cn("inline-block", animationClasses.container, className)}>
      {prefix}
      <span className={cn("transition-all duration-300", animationClasses.text)}>
        {texts[currentIndex]}
      </span>
      {suffix}
    </span>
  )
}

// Typewriter effect component for more complex animations
export function TypewriterText({ 
  texts, 
  interval = 4000,
  typeSpeed = 100,
  deleteSpeed = 50,
  className = "",
  prefix = "",
  suffix = ""
}: RotatingTextProps & { typeSpeed?: number; deleteSpeed?: number }) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [currentText, setCurrentText] = useState("")
  const [isDeleting, setIsDeleting] = useState(false)
  const [isPaused, setIsPaused] = useState(false)

  useEffect(() => {
    if (texts.length === 0) return

    const currentFullText = texts[currentIndex]
    
    const timer = setTimeout(() => {
      if (isPaused) {
        setIsPaused(false)
        setIsDeleting(true)
        return
      }

      if (isDeleting) {
        if (currentText.length > 0) {
          setCurrentText(currentText.slice(0, -1))
        } else {
          setIsDeleting(false)
          setCurrentIndex((prevIndex) => (prevIndex + 1) % texts.length)
        }
      } else {
        if (currentText.length < currentFullText.length) {
          setCurrentText(currentFullText.slice(0, currentText.length + 1))
        } else {
          setIsPaused(true)
        }
      }
    }, isPaused ? interval : isDeleting ? deleteSpeed : typeSpeed)

    return () => clearTimeout(timer)
  }, [currentText, isDeleting, isPaused, currentIndex, texts, interval, typeSpeed, deleteSpeed])

  return (
    <span className={cn("inline-block", className)}>
      {prefix}
      <span className="relative">
        {currentText}
        <span className="animate-pulse ml-1 text-current">|</span>
      </span>
      {suffix}
    </span>
  )
}

// Slide-up animation component
export function SlideUpText({ 
  texts, 
  interval = 3000,
  className = "",
  prefix = "",
  suffix = ""
}: RotatingTextProps) {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    if (texts.length <= 1) return

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % texts.length)
    }, interval)

    return () => clearInterval(timer)
  }, [texts.length, interval])

  if (texts.length === 0) return null

  return (
    <span className={cn("inline-block relative overflow-hidden", className)}>
      {prefix}
      <span className="relative inline-block">
        {texts.map((text, index) => (
          <span
            key={index}
            className={cn(
              "absolute left-0 top-0 transition-transform duration-700 ease-in-out whitespace-nowrap",
              index === currentIndex 
                ? "transform translate-y-0 opacity-100" 
                : index === (currentIndex - 1 + texts.length) % texts.length
                ? "transform -translate-y-full opacity-0"
                : "transform translate-y-full opacity-0"
            )}
            style={{
              transitionDelay: index === currentIndex ? "0ms" : "0ms"
            }}
          >
            {text}
          </span>
        ))}
        {/* Invisible text to maintain width */}
        <span className="opacity-0 pointer-events-none">
          {texts.reduce((longest, current) => 
            current.length > longest.length ? current : longest, ""
          )}
        </span>
      </span>
      {suffix}
    </span>
  )
}

// Gradient text animation component
export function GradientRotatingText({ 
  texts, 
  interval = 3000,
  className = "",
  gradientColors = "from-blue-400 via-purple-400 to-pink-400",
  prefix = "",
  suffix = ""
}: RotatingTextProps & { gradientColors?: string }) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (texts.length <= 1) return

    const timer = setInterval(() => {
      setIsAnimating(true)
      
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % texts.length)
        setIsAnimating(false)
      }, 400)
    }, interval)

    return () => clearInterval(timer)
  }, [texts.length, interval])

  if (texts.length === 0) return null

  return (
    <span className={cn("inline-block", className)}>
      {prefix}
      <span 
        className={cn(
          "bg-gradient-to-r bg-clip-text text-transparent transition-all duration-400",
          gradientColors,
          isAnimating ? "opacity-0 scale-95" : "opacity-100 scale-100"
        )}
      >
        {texts[currentIndex]}
      </span>
      {suffix}
    </span>
  )
}
