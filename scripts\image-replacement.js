#!/usr/bin/env node

/**
 * Image Replacement Script for Grace Community Church Website
 * 
 * This script helps organize and optimize images for the church website.
 * It creates the proper directory structure and provides utilities for
 * image processing and validation.
 */

const fs = require('fs');
const path = require('path');

// Define the image directory structure
const imageDirectories = [
  'public/images/hero',
  'public/images/about',
  'public/images/ministries',
  'public/images/events',
  'public/images/sermons',
  'public/images/staff',
  'public/images/facilities',
  'public/images/general'
];

// Define required images with their specifications
const requiredImages = {
  // Hero section images (1920x1080)
  'hero/church-exterior.jpg': {
    description: 'Main church building exterior',
    dimensions: '1920x1080',
    usage: 'Homepage hero background'
  },
  'hero/worship-service.jpg': {
    description: 'Congregation during worship service',
    dimensions: '1920x1080',
    usage: 'Ministries page hero'
  },
  'hero/community-gathering.jpg': {
    description: 'People fellowshipping together',
    dimensions: '1920x1080',
    usage: 'Events page hero'
  },

  // About page images
  'about/church-community.jpg': {
    description: 'Church community gathering in fellowship',
    dimensions: '600x400',
    usage: 'Homepage about section'
  },
  'about/church-history.jpg': {
    description: 'Historical photo showing church growth',
    dimensions: '600x500',
    usage: 'About page history section'
  },

  // Staff photos (400x400)
  'staff/pastor-john-smith.jpg': {
    description: 'Pastor John Smith professional headshot',
    dimensions: '400x400',
    usage: 'About page leadership section'
  },
  'staff/sarah-johnson-worship.jpg': {
    description: 'Sarah Johnson (Worship Pastor) headshot',
    dimensions: '400x400',
    usage: 'About page leadership section'
  },
  'staff/mike-davis-youth.jpg': {
    description: 'Mike Davis (Youth Pastor) headshot',
    dimensions: '400x400',
    usage: 'About page leadership section'
  },
  'staff/lisa-brown-children.jpg': {
    description: 'Lisa Brown (Children\'s Director) headshot',
    dimensions: '400x400',
    usage: 'About page leadership section'
  },

  // Ministry images (800x600)
  'ministries/children-ministry-classroom.jpg': {
    description: 'Children\'s ministry in action',
    dimensions: '800x600',
    usage: 'Ministries page'
  },
  'ministries/youth-group-activity.jpg': {
    description: 'Youth group engaged in activity',
    dimensions: '800x600',
    usage: 'Ministries page'
  },
  'ministries/worship-team-performing.jpg': {
    description: 'Worship team during service',
    dimensions: '800x600',
    usage: 'Ministries page'
  },
  'ministries/small-group-bible-study.jpg': {
    description: 'Small group Bible study session',
    dimensions: '800x600',
    usage: 'Ministries page'
  },
  'ministries/community-outreach-service.jpg': {
    description: 'Community service/outreach event',
    dimensions: '800x600',
    usage: 'Ministries page'
  },
  'ministries/hospitality-coffee-fellowship.jpg': {
    description: 'Coffee fellowship time',
    dimensions: '800x600',
    usage: 'Ministries page'
  },

  // Event images (600x400)
  'events/easter-celebration-service.jpg': {
    description: 'Easter service celebration',
    dimensions: '600x400',
    usage: 'Events page'
  },
  'events/community-food-drive.jpg': {
    description: 'Food drive volunteers and donations',
    dimensions: '600x400',
    usage: 'Events page'
  },
  'events/youth-spring-retreat.jpg': {
    description: 'Youth retreat activities',
    dimensions: '600x400',
    usage: 'Events page'
  },
  'events/womens-bible-study.jpg': {
    description: 'Women\'s Bible study group',
    dimensions: '600x400',
    usage: 'Events page'
  },
  'events/mens-breakfast-fellowship.jpg': {
    description: 'Men\'s breakfast gathering',
    dimensions: '600x400',
    usage: 'Events page'
  },
  'events/childrens-easter-egg-hunt.jpg': {
    description: 'Children\'s Easter egg hunt',
    dimensions: '600x400',
    usage: 'Events page'
  },
  'events/worship-night-service.jpg': {
    description: 'Special worship night',
    dimensions: '600x400',
    usage: 'Events page'
  },
  'events/vacation-bible-school.jpg': {
    description: 'VBS activities with children',
    dimensions: '600x400',
    usage: 'Events page'
  },

  // Sermon series artwork (600x400)
  'sermons/hope-in-hard-times-series.jpg': {
    description: 'Hope in Hard Times series artwork',
    dimensions: '600x400',
    usage: 'Sermons page'
  },
  'sermons/gospel-of-john-series.jpg': {
    description: 'Gospel of John series artwork',
    dimensions: '600x400',
    usage: 'Sermons page'
  },
  'sermons/psalms-for-life-series.jpg': {
    description: 'Psalms for Life series artwork',
    dimensions: '600x400',
    usage: 'Sermons page'
  },
  'sermons/easter-2024-series.jpg': {
    description: 'Easter 2024 series artwork',
    dimensions: '600x400',
    usage: 'Sermons page'
  },
  'sermons/christmas-2023-series.jpg': {
    description: 'Christmas 2023 series artwork',
    dimensions: '600x400',
    usage: 'Sermons page'
  }
};

/**
 * Create the directory structure for images
 */
function createDirectories() {
  console.log('Creating image directory structure...');
  
  imageDirectories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✓ Created directory: ${dir}`);
    } else {
      console.log(`✓ Directory exists: ${dir}`);
    }
  });
}

/**
 * Generate a report of missing images
 */
function generateMissingImagesReport() {
  console.log('\n=== MISSING IMAGES REPORT ===\n');
  
  const missingImages = [];
  const existingImages = [];
  
  Object.entries(requiredImages).forEach(([imagePath, details]) => {
    const fullPath = path.join('public/images', imagePath);
    
    if (fs.existsSync(fullPath)) {
      existingImages.push({ path: imagePath, ...details });
    } else {
      missingImages.push({ path: imagePath, ...details });
    }
  });
  
  console.log(`📊 SUMMARY:`);
  console.log(`   Total required images: ${Object.keys(requiredImages).length}`);
  console.log(`   Existing images: ${existingImages.length}`);
  console.log(`   Missing images: ${missingImages.length}`);
  
  if (missingImages.length > 0) {
    console.log(`\n❌ MISSING IMAGES (${missingImages.length}):`);
    missingImages.forEach(img => {
      console.log(`   📁 ${img.path}`);
      console.log(`      Description: ${img.description}`);
      console.log(`      Dimensions: ${img.dimensions}`);
      console.log(`      Usage: ${img.usage}`);
      console.log('');
    });
  }
  
  if (existingImages.length > 0) {
    console.log(`\n✅ EXISTING IMAGES (${existingImages.length}):`);
    existingImages.forEach(img => {
      console.log(`   📁 ${img.path}`);
    });
  }
  
  return { missingImages, existingImages };
}

/**
 * Generate HTML report for easy viewing
 */
function generateHTMLReport() {
  const { missingImages, existingImages } = generateMissingImagesReport();
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grace Community Church - Image Replacement Report</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #2563eb, #7c3aed); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .stat-card { background: #f8fafc; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #2563eb; }
        .stat-number { font-size: 2em; font-weight: bold; color: #2563eb; }
        .image-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .image-card { background: white; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; }
        .missing { border-left: 4px solid #ef4444; }
        .existing { border-left: 4px solid #10b981; }
        .image-path { font-family: monospace; background: #f1f5f9; padding: 5px 8px; border-radius: 4px; font-size: 0.9em; }
        .dimensions { color: #6b7280; font-size: 0.9em; }
        .usage { color: #374151; font-style: italic; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Grace Community Church</h1>
        <h2>Image Replacement Report</h2>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
    </div>
    
    <div class="summary">
        <div class="stat-card">
            <div class="stat-number">${Object.keys(requiredImages).length}</div>
            <div>Total Images</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${existingImages.length}</div>
            <div>Existing</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${missingImages.length}</div>
            <div>Missing</div>
        </div>
    </div>
    
    <h3>Missing Images (${missingImages.length})</h3>
    <div class="image-grid">
        ${missingImages.map(img => `
            <div class="image-card missing">
                <div class="image-path">${img.path}</div>
                <p><strong>Description:</strong> ${img.description}</p>
                <p class="dimensions"><strong>Dimensions:</strong> ${img.dimensions}</p>
                <p class="usage"><strong>Usage:</strong> ${img.usage}</p>
            </div>
        `).join('')}
    </div>
    
    <h3>Existing Images (${existingImages.length})</h3>
    <div class="image-grid">
        ${existingImages.map(img => `
            <div class="image-card existing">
                <div class="image-path">${img.path}</div>
                <p><strong>Description:</strong> ${img.description}</p>
                <p class="dimensions"><strong>Dimensions:</strong> ${img.dimensions}</p>
                <p class="usage"><strong>Usage:</strong> ${img.usage}</p>
            </div>
        `).join('')}
    </div>
</body>
</html>`;
  
  fs.writeFileSync('image-replacement-report.html', html);
  console.log('\n📄 HTML report generated: image-replacement-report.html');
}

/**
 * Main execution
 */
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Grace Community Church - Image Replacement Script

Usage:
  node scripts/image-replacement.js [options]

Options:
  --create-dirs     Create the image directory structure
  --report          Generate missing images report
  --html-report     Generate HTML report
  --help, -h        Show this help message

Examples:
  node scripts/image-replacement.js --create-dirs
  node scripts/image-replacement.js --report
  node scripts/image-replacement.js --html-report
`);
    return;
  }
  
  if (args.includes('--create-dirs')) {
    createDirectories();
  }
  
  if (args.includes('--report')) {
    generateMissingImagesReport();
  }
  
  if (args.includes('--html-report')) {
    generateHTMLReport();
  }
  
  if (args.length === 0) {
    console.log('Grace Community Church - Image Replacement Script');
    console.log('Run with --help for usage information');
    createDirectories();
    generateMissingImagesReport();
  }
}

// Run the script
main();
