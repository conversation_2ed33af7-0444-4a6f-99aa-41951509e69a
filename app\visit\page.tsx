"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  MapPin, 
  Clock, 
  Car, 
  Baby, 
  Coffee, 
  Users, 
  Heart,
  Phone,
  Mail,
  Navigation,
  Accessibility,
  Music,
  BookOpen
} from "lucide-react"
import Image from "next/image"

export default function VisitPage() {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    visitDate: "",
    service: "",
    adults: "1",
    children: "0",
    childrenAges: "",
    specialNeeds: "",
    questions: "",
    newsletter: false,
    followUp: false
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log("Form submitted:", formData)
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Plan Your Visit</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            We can't wait to meet you! Here's everything you need to know for your first visit.
          </p>
        </div>
      </section>

      {/* What to Expect */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center">What to Expect</h2>
            
            <div className="grid md:grid-cols-2 gap-8 mb-12">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Heart className="w-6 h-6 text-red-500" />
                  Warm Welcome
                </h3>
                <p className="text-gray-700 mb-6">
                  Our friendly greeting team will welcome you at the door and help you find your way around. 
                  Don't worry about not knowing anyone - we're excited to meet you!
                </p>

                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Coffee className="w-6 h-6 text-orange-500" />
                  Coffee & Fellowship
                </h3>
                <p className="text-gray-700 mb-6">
                  Arrive a few minutes early to grab some free coffee, donuts, and connect with others 
                  in our welcoming lobby area.
                </p>

                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Users className="w-6 h-6 text-blue-500" />
                  Casual Atmosphere
                </h3>
                <p className="text-gray-700">
                  Come as you are! You'll see people in everything from jeans to suits. 
                  We care more about your heart than your outfit.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Music className="w-6 h-6 text-purple-500" />
                  Inspiring Worship
                </h3>
                <p className="text-gray-700 mb-6">
                  Our worship combines contemporary and traditional elements with passionate singing, 
                  skilled musicians, and heartfelt prayer.
                </p>

                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <BookOpen className="w-6 h-6 text-green-500" />
                  Biblical Teaching
                </h3>
                <p className="text-gray-700 mb-6">
                  Expect practical, relevant messages from God's Word that you can apply to your daily life. 
                  Our pastor teaches with clarity and passion.
                </p>

                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Baby className="w-6 h-6 text-pink-500" />
                  Kids Welcome
                </h3>
                <p className="text-gray-700">
                  Children are always welcome in service, and we provide excellent childcare and 
                  age-appropriate programs for all ages.
                </p>
              </div>
            </div>

            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-3">Service Length</h3>
              <p className="text-gray-700">
                Our services typically last about 75 minutes, including worship music, announcements, 
                prayer, and a 30-35 minute message. We start and end on time!
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Service Times & Location */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center">Service Times & Location</h2>
          
          <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* Service Times */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-6 h-6 text-blue-600" />
                  Service Times
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-lg">Sunday Services</h4>
                  <p className="text-gray-600">9:00 AM - Traditional Service</p>
                  <p className="text-gray-600">11:00 AM - Contemporary Service</p>
                  <p className="text-sm text-gray-500 mt-2">Both services feature the same message with different worship styles</p>
                </div>
                
                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold text-lg">Wednesday Evening</h4>
                  <p className="text-gray-600">7:00 PM - Prayer & Bible Study</p>
                  <p className="text-sm text-gray-500 mt-2">Casual midweek gathering for deeper study and prayer</p>
                </div>

                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">First-Time Visitors</h4>
                  <p className="text-sm text-gray-700">
                    We recommend the 11:00 AM service for first-time visitors as it tends to be 
                    more energetic and has more families with children.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Location & Directions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-6 h-6 text-blue-600" />
                  Location & Directions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold text-lg mb-2">Address</h4>
                  <p className="text-gray-700">
                    123 Faith Street<br />
                    Springfield, IL 62701
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold text-lg mb-2 flex items-center gap-2">
                    <Car className="w-5 h-5" />
                    Parking
                  </h4>
                  <p className="text-gray-700 text-sm">
                    Free parking available in our main lot and street parking. 
                    Handicap accessible spaces available near the main entrance.
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold text-lg mb-2 flex items-center gap-2">
                    <Accessibility className="w-5 h-5" />
                    Accessibility
                  </h4>
                  <p className="text-gray-700 text-sm">
                    Our building is fully wheelchair accessible with ramps, elevators, 
                    and accessible restrooms. Hearing assistance devices available.
                  </p>
                </div>

                <Button className="w-full flex items-center gap-2">
                  <Navigation className="w-4 h-4" />
                  Get Directions
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Children & Families */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center">Children & Families</h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Baby className="w-6 h-6 text-pink-500" />
                    Childcare & Programs
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Nursery (Ages 0-2)</h4>
                    <p className="text-sm text-gray-600">
                      Safe, loving care with background-checked volunteers. 
                      Changing stations, toys, and quiet space available.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Preschool (Ages 3-5)</h4>
                    <p className="text-sm text-gray-600">
                      Age-appropriate Bible stories, songs, crafts, and games 
                      in a fun, engaging environment.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Elementary (Ages 6-12)</h4>
                    <p className="text-sm text-gray-600">
                      Interactive lessons, worship, and activities designed 
                      to help kids grow in their faith.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>What to Bring</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <p className="text-sm text-gray-700">
                      <strong>Diaper bag essentials</strong> - We have changing tables and basic supplies
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <p className="text-sm text-gray-700">
                      <strong>Snacks for little ones</strong> - Quiet snacks are welcome during service
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <p className="text-sm text-gray-700">
                      <strong>Nothing else required!</strong> - We provide Bibles, bulletins, and materials
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Visitor Registration Form */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 text-center">Let Us Know You're Coming</h2>
            <p className="text-lg text-gray-600 mb-8 text-center">
              While not required, letting us know helps us prepare for your visit and answer any questions you might have.
            </p>

            <Card>
              <CardHeader>
                <CardTitle>Visitor Information</CardTitle>
                <CardDescription>
                  Fill out this form and we'll make sure everything is ready for your visit.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange("firstName", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange("lastName", e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="visitDate">Planned Visit Date</Label>
                      <Input
                        id="visitDate"
                        type="date"
                        value={formData.visitDate}
                        onChange={(e) => handleInputChange("visitDate", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="service">Preferred Service</Label>
                      <Select value={formData.service} onValueChange={(value) => handleInputChange("service", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a service" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="9am">9:00 AM Traditional</SelectItem>
                          <SelectItem value="11am">11:00 AM Contemporary</SelectItem>
                          <SelectItem value="both">Not sure yet</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="adults">Number of Adults</Label>
                      <Select value={formData.adults} onValueChange={(value) => handleInputChange("adults", value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1</SelectItem>
                          <SelectItem value="2">2</SelectItem>
                          <SelectItem value="3">3</SelectItem>
                          <SelectItem value="4">4</SelectItem>
                          <SelectItem value="5+">5+</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="children">Number of Children</Label>
                      <Select value={formData.children} onValueChange={(value) => handleInputChange("children", value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">0</SelectItem>
                          <SelectItem value="1">1</SelectItem>
                          <SelectItem value="2">2</SelectItem>
                          <SelectItem value="3">3</SelectItem>
                          <SelectItem value="4">4</SelectItem>
                          <SelectItem value="5+">5+</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {formData.children !== "0" && (
                    <div>
                      <Label htmlFor="childrenAges">Children's Ages</Label>
                      <Input
                        id="childrenAges"
                        placeholder="e.g., 3, 7, 12"
                        value={formData.childrenAges}
                        onChange={(e) => handleInputChange("childrenAges", e.target.value)}
                      />
                    </div>
                  )}

                  <div>
                    <Label htmlFor="specialNeeds">Special Needs or Accommodations</Label>
                    <Textarea
                      id="specialNeeds"
                      placeholder="Let us know if you need wheelchair access, hearing assistance, or other accommodations"
                      value={formData.specialNeeds}
                      onChange={(e) => handleInputChange("specialNeeds", e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="questions">Questions or Comments</Label>
                    <Textarea
                      id="questions"
                      placeholder="Any questions about your visit or our church?"
                      value={formData.questions}
                      onChange={(e) => handleInputChange("questions", e.target.value)}
                    />
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="newsletter"
                        checked={formData.newsletter}
                        onCheckedChange={(checked) => handleInputChange("newsletter", checked as boolean)}
                      />
                      <Label htmlFor="newsletter" className="text-sm">
                        Subscribe to our newsletter for updates and events
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="followUp"
                        checked={formData.followUp}
                        onCheckedChange={(checked) => handleInputChange("followUp", checked as boolean)}
                      />
                      <Label htmlFor="followUp" className="text-sm">
                        I'd like someone to follow up with me after my visit
                      </Label>
                    </div>
                  </div>

                  <Button type="submit" className="w-full">
                    Submit Visitor Information
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Info */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Questions?</h2>
            <p className="text-lg text-gray-600 mb-8">
              Don't hesitate to reach out if you have any questions about your visit.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                (*************
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                <EMAIL>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
