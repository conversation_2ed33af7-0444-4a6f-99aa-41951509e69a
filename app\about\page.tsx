import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Heart, Users, BookOpen, Globe } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function AboutPage() {
  const staff = [
    {
      name: "Pastor <PERSON>",
      role: "Senior Pastor",
      bio: "Pastor <PERSON> has been leading Grace Community Church for over 15 years. He holds a Master of Divinity from Seminary and is passionate about expository preaching and discipleship.",
      image: "/images/staff/pastor-john-smith.jpg",
    },
    {
      name: "<PERSON>",
      role: "Worship Pastor",
      bio: "<PERSON> leads our worship ministry and has a heart for helping people encounter God through music. She has been with our church family for 8 years.",
      image: "/images/staff/sarah-johnson-worship.jpg",
    },
    {
      name: "<PERSON>",
      role: "Youth Pastor",
      bio: "<PERSON> is passionate about investing in the next generation. He has been working with youth for over 10 years and loves seeing young people grow in their faith.",
      image: "/images/staff/mike-davis-youth.jpg",
    },
    {
      name: "<PERSON>",
      role: "Children's Director",
      bio: "<PERSON> oversees all of our children's ministries and has a gift for making faith fun and accessible for kids. She has been serving families for 12 years.",
      image: "/images/staff/lisa-brown-children.jpg",
    },
  ]

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">About Grace Community Church</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Learn about our history, mission, and the people who make our church family special
          </p>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
              <p className="text-lg text-gray-700 mb-6">
                Grace Community Church was founded in 1985 by a small group of families who had a vision to create a
                church that would be a beacon of hope in Springfield. What started as 20 people meeting in a living room
                has grown into a thriving community of over 800 members.
              </p>
              <p className="text-lg text-gray-700 mb-6">
                Throughout our history, we have remained committed to our core values of loving God, loving people, and
                serving our community. We believe that the church should be a place where everyone can belong, grow, and
                make a difference.
              </p>
              <p className="text-lg text-gray-700">
                Today, we continue to be a multi-generational church that welcomes people from all walks of life. Our
                heart is to see lives transformed by the love of Jesus Christ.
              </p>
            </div>
            <div className="relative">
              <Image
                src="/images/about-church-history.jpg"
                alt="Grace Community Church historical photo showing growth over the years"
                width={600}
                height={500}
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <p className="text-lg text-gray-600">These values guide everything we do as a church community</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardHeader>
                <Heart className="w-16 h-16 mx-auto text-red-500 mb-4" />
                <CardTitle>Love God</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  We are passionate about worshiping God and growing in our relationship with Him through prayer,
                  worship, and studying His Word.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Users className="w-16 h-16 mx-auto text-blue-500 mb-4" />
                <CardTitle>Love People</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  We believe in creating authentic relationships and building a community where everyone feels valued,
                  accepted, and loved.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <BookOpen className="w-16 h-16 mx-auto text-green-500 mb-4" />
                <CardTitle>Grow in Faith</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  We are committed to helping people grow spiritually through biblical teaching, discipleship, and
                  life-changing experiences.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Globe className="w-16 h-16 mx-auto text-purple-500 mb-4" />
                <CardTitle>Serve Others</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  We believe in making a difference in our community and around the world through acts of service and
                  compassion.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Leadership Team</h2>
            <p className="text-lg text-gray-600">Meet the dedicated staff who serve our church family</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {staff.map((person, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <Image
                    src={person.image || "/placeholder.svg"}
                    alt={person.name}
                    width={200}
                    height={200}
                    className="rounded-full mx-auto mb-4"
                  />
                  <CardTitle className="text-xl">{person.name}</CardTitle>
                  <CardDescription className="text-blue-600 font-semibold">{person.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">{person.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* What We Believe */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">What We Believe</h2>
              <p className="text-lg text-gray-600">Our foundational beliefs guide our faith and practice</p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">The Bible</h3>
                <p className="text-gray-700 mb-6">
                  We believe the Bible is the inspired, infallible Word of God and our ultimate authority for faith and
                  life.
                </p>

                <h3 className="text-xl font-bold text-gray-900 mb-3">God</h3>
                <p className="text-gray-700 mb-6">
                  We believe in one God who exists in three persons: Father, Son, and Holy Spirit.
                </p>

                <h3 className="text-xl font-bold text-gray-900 mb-3">Jesus Christ</h3>
                <p className="text-gray-700">
                  We believe Jesus Christ is fully God and fully man, who died for our sins and rose again, offering
                  salvation to all who believe.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Salvation</h3>
                <p className="text-gray-700 mb-6">
                  We believe salvation is by grace through faith in Jesus Christ alone, not by works.
                </p>

                <h3 className="text-xl font-bold text-gray-900 mb-3">The Church</h3>
                <p className="text-gray-700 mb-6">
                  We believe the church is the body of Christ, called to worship, fellowship, discipleship, and mission.
                </p>

                <h3 className="text-xl font-bold text-gray-900 mb-3">Eternity</h3>
                <p className="text-gray-700">
                  We believe in the resurrection of the dead and eternal life for all who trust in Jesus Christ.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Join Our Church Family</h2>
          <p className="text-xl mb-8 text-blue-100">We'd love to meet you and help you take your next step in faith.</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
                Contact Us
              </Button>
            </Link>
            <Link href="/visit">
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                Plan Your Visit
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
